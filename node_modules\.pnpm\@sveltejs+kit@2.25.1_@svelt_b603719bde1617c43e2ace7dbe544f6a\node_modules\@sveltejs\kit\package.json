{"name": "@sveltejs/kit", "version": "2.25.1", "description": "SvelteKit is the fastest way to build Svelte apps", "keywords": ["framework", "official", "svelte", "sveltekit", "vite"], "repository": {"type": "git", "url": "https://github.com/sveltejs/kit", "directory": "packages/kit"}, "license": "MIT", "homepage": "https://svelte.dev", "type": "module", "dependencies": {"@sveltejs/acorn-typescript": "^1.0.5", "@types/cookie": "^0.6.0", "acorn": "^8.14.1", "cookie": "^0.6.0", "devalue": "^5.1.0", "esm-env": "^1.2.2", "kleur": "^4.1.5", "magic-string": "^0.30.5", "mrmime": "^2.0.0", "sade": "^1.8.1", "set-cookie-parser": "^2.6.0", "sirv": "^3.0.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@sveltejs/vite-plugin-svelte": "^6.0.0-next.3", "@types/connect": "^3.4.38", "@types/node": "^18.19.119", "@types/set-cookie-parser": "^2.4.7", "dts-buddy": "^0.6.1", "rollup": "^4.14.2", "svelte": "^5.35.5", "svelte-preprocess": "^6.0.0", "typescript": "^5.3.3", "vite": "^6.3.5", "vitest": "^3.2.3"}, "peerDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.0 || ^4.0.0-next.1 || ^5.0.0 || ^6.0.0-next.0", "svelte": "^4.0.0 || ^5.0.0-next.0", "vite": "^5.0.3 || ^6.0.0 || ^7.0.0-beta.0"}, "bin": {"svelte-kit": "svelte-kit.js"}, "files": ["src", "!src/**/*.spec.js", "!src/core/**/fixtures", "!src/core/**/test", "types", "svelte-kit.js"], "exports": {"./package.json": "./package.json", ".": {"types": "./types/index.d.ts", "import": "./src/exports/index.js"}, "./internal": {"types": "./types/index.d.ts", "import": "./src/exports/internal/index.js"}, "./node": {"types": "./types/index.d.ts", "import": "./src/exports/node/index.js"}, "./node/polyfills": {"types": "./types/index.d.ts", "import": "./src/exports/node/polyfills.js"}, "./hooks": {"types": "./types/index.d.ts", "import": "./src/exports/hooks/index.js"}, "./vite": {"types": "./types/index.d.ts", "import": "./src/exports/vite/index.js"}}, "types": "types/index.d.ts", "engines": {"node": ">=18.13"}, "scripts": {"lint": "prettier --config ../../.prettierrc --check .", "check": "tsc && cd ./test/types && tsc", "check:all": "tsc && pnpm -r --filter=\"./**\" check", "format": "prettier --config ../../.prettierrc --write .", "test": "pnpm test:unit && pnpm test:integration", "test:integration": "pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test", "test:cross-platform:dev": "pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test:cross-platform:dev", "test:cross-platform:build": "pnpm test:unit && pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test:cross-platform:build", "test:server-side-route-resolution:dev": "pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test:server-side-route-resolution:dev", "test:server-side-route-resolution:build": "pnpm test:unit && pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test:server-side-route-resolution:build", "test:unit": "vitest --config kit.vitest.config.js run", "generate:version": "node scripts/generate-version.js", "generate:types": "node scripts/generate-dts.js"}}