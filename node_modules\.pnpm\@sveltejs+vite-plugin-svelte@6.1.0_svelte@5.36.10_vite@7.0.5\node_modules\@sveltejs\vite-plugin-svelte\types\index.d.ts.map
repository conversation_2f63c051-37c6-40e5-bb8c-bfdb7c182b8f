{"version": 3, "file": "index.d.ts", "names": ["Options", "PluginOptionsInline", "PluginOptions", "SvelteConfig", "ExperimentalOptions", "CompileModuleOptions", "Arrayable", "VitePreprocessOptions", "svelte", "vitePreprocess", "loadSvelteConfig"], "sources": ["../src/public.d.ts", "../src/index.js", "../src/preprocess.js", "../src/utils/load-svelte-config.js"], "sourcesContent": [null, null, null, null], "mappings": ";;;;aAIYA,OAAOA;;WAETC,mBAAmBA;;;;;;;;;;;kBAWZC,aAAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kBAgGbC,YAAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAiDnBC,mBAAmBA;;;;;;;;;;;;;;;;;;;;;;;WAuBnBC,oBAAoBA;;;;;;;;;;;;;;;MAezBC,SAASA;;kBAEGC,qBAAqBA;;;;;;;;;;;;;;;;;;iBCvLtBC,MAAMA;iBCMNC,cAAcA;iBCFRC,gBAAgBA", "ignoreList": []}