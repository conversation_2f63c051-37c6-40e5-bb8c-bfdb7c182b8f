# @sveltejs/vite-plugin-svelte-inspector

A [Svelte](https://svelte.dev) inspector plugin for [Vite](https://vitejs.dev).

## Usage

```js
// vite.config.js
import { defineConfig } from 'vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import { svelteInspector } from '@sveltejs/vite-plugin-svelte-inspector';

export default defineConfig({
  plugins: [
    // the svelte plugin is required to work
    svelte(),
    svelteInspector({
      /* plugin options */
    })
  ]
});
```

## License

[MIT](./LICENSE)
